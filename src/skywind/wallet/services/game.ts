import {
    DefaultMerchantStartGameService,
    LoginTerminalRequest,
    LoginTerminalResponse,
    MerchantGameTokenInfo,
    MerchantInfo,
    PlayerInfo,
    PlayMode
} from "@skywind-group/sw-wallet-adapter-core";
import { measures } from "@skywind-group/sw-utils";
import {
    SeamlessAuthTokenData,
    SeamlessGameInitRequest,
    SeamlessGameUrlInfo,
    SeamlessRenewSessionRequest,
    SeamlessRenewSessionResponse,
    SeamlessStartGameTokenData,
    SeamlessUrlParams,
    SeamlessVerifyTokenRequest,
    SeamlessVerifyTokenResponse
} from "../../entities/seamless";
import { GameInfo } from "../../entities/sw";
import { SeamlessAPIService } from "./seamlessAPIService";
import { CurrencyConverter } from "../../utils/currencyConverter";
import { MerchantInternalError } from "../../errors";
import { applyRegulation } from "../../regulations";
import { getDomain } from "../../utils/getDomain";
import { countryConverter } from "../../utils/countryConverter";
import { playModeConverter } from "../../utils/playModeConverter";
import measure = measures.measure;

export class GameService
    extends DefaultMerchantStartGameService<SeamlessGameInitRequest,
        SeamlessStartGameTokenData,
        SeamlessAuthTokenData,
        SeamlessGameUrlInfo> {

    constructor(public service: SeamlessAPIService) {
        super();
    }

    @measure({ isAsync: true, name: "GameService.createGameUrl" })
    public async createGameUrl(merchant: MerchantInfo,
                               gameCode: string,
                               providerCode: string,
                               providerGameCode: string,
                               initRequest: SeamlessGameInitRequest,
                               player?: PlayerInfo): Promise<SeamlessGameUrlInfo> {

        const tokenData = await this.getStartGameTokenData(merchant,
            gameCode, providerCode, providerGameCode, initRequest);

        return {
            tokenData,
            urlParams: this.getUrlParams(initRequest, tokenData.playmode)
        };
    }

    private getUrlParams(initRequest: SeamlessGameInitRequest, playmode: PlayMode): SeamlessUrlParams {
        const language = this.isChooseGame(initRequest) ?
            initRequest.previousStartTokenData.language :
            initRequest.language;
        return {
            lobby: initRequest.lobby,
            cashier: initRequest.cashier,
            playmode,
            language,
            merch_login_url: initRequest.merch_login_url
        };
    }

    @measure({ name: "GameService.getStartGameTokenData", isAsync: true })
    public async getStartGameTokenData(merchant: MerchantInfo,
                                       gameCode: string,
                                       providerCode: string,
                                       providerGameCode: string,
                                       initRequest: SeamlessGameInitRequest): Promise<SeamlessStartGameTokenData> {
        const gameInfo: GameInfo = {
            gameCode,
            providerCode,
            providerGameCode
        };

        if (this.isChooseGame(initRequest)) {
            return this.getStartGameTokenDataFromPreviousToken(merchant, gameInfo, initRequest);
        }

        if (this.isLobbyRequest(initRequest)) {
            return {
                ipmSessionId: await this.getCustomerSessionId(merchant, gameInfo, initRequest),
                brandId: merchant.brandId,
                merchantType: merchant.type,
                merchantCode: merchant.code,
                gameCode: gameCode,
                providerGameCode: providerGameCode,
                providerCode: providerCode,
                playmode: initRequest.playmode,
                playerCode: initRequest.playerCode,
                siteUrl: getDomain(initRequest.lobby || initRequest.cashier),
                oldGameCode: gameInfo.gameCode
            } as SeamlessStartGameTokenData;
        }

        if (initRequest.ticket) {
            const data = await this.verifyToken(merchant, initRequest.ticket, initRequest.ip);
            const currency = CurrencyConverter.fromIPMCurrency(data.currency_code, merchant);
            console.log("~~~", currency);
            return this.mapSeamlessToSWToken(merchant, data, {
                ...gameInfo,
                currency,
                language: initRequest.language,
                hasLobby: !!initRequest.lobby && !merchant.params.skipLobbyButtonForBetLimitError,
                siteUrl: getDomain(initRequest.lobby || initRequest.cashier),
                playMode: playModeConverter(initRequest.playmode, currency, merchant),
            });
        }

        if (initRequest.playmode === PlayMode.FUN) {
            return {
                playerCode: `player-${Date.now()}`,
                brandId: merchant.brandId,
                playmode: playModeConverter(
                    initRequest.playmode,
                    CurrencyConverter.fromIPMCurrency(initRequest.currency, merchant),
                    merchant
                ),
                language: initRequest.language,
                merchantType: merchant.type,
                merchantCode: merchant.code,
                currency: initRequest.currency,
                gameGroup: initRequest.gameGroup,
                ...gameInfo
            } as SeamlessStartGameTokenData;
        }

    }

    private async verifyToken(merchant: MerchantInfo,
                              ticket: string,
                              ip?: string,
                              url: string = "api/validate_ticket"): Promise<SeamlessVerifyTokenResponse> {
        const data: SeamlessVerifyTokenRequest = {
            ticket: ticket,
            merch_id: merchant.code,
            merch_pwd: merchant.params?.password
        };
        if (ip) {
            data.ip = ip;
        }
        return this.service.doPost<SeamlessVerifyTokenResponse>(url, data, merchant);
    }

    private async refreshSession(merchant: MerchantInfo,
                                 gameInfo: GameInfo,
                                 initRequest: SeamlessGameInitRequest): Promise<SeamlessRenewSessionResponse> {
        const data: SeamlessRenewSessionRequest = {
            old_game_code: initRequest.oldGameCode || initRequest.previousStartTokenData?.oldGameCode,
            new_game_code: gameInfo.gameCode,
            merch_id: merchant.code,
            merch_pwd: merchant.params?.password,
            cust_id: initRequest.playerCode,
            cust_session_id: initRequest.customerSessionId || initRequest.ipmSessionId
        };
        return this.service.doPost<SeamlessRenewSessionResponse>("api/refresh_session", data, merchant);
    }

    private async getCustomerSessionId(merchant: MerchantInfo,
                                       gameInfo: GameInfo,
                                       initRequest: SeamlessGameInitRequest): Promise<string> {
        if (merchant.params.refreshSessionForNewGame && initRequest.oldGameCode !== gameInfo.gameCode) {
            const data = await this.refreshSession(merchant, gameInfo, initRequest);
            return data.new_cust_session_id;
        }
        return initRequest.customerSessionId || initRequest.ipmSessionId;
    }

    private mapSeamlessToSWToken(
        merchant: MerchantInfo,
        data: SeamlessVerifyTokenResponse,
        { playMode, language, ...info }: {
            currency: string;
            hasLobby: boolean;
            siteUrl: string;
            playMode?: PlayMode
            language?: string;
        } & GameInfo
    ): SeamlessStartGameTokenData {
        const tokenData: SeamlessStartGameTokenData = {
            ...info,
            country: countryConverter(data.country),
            test: `${data.test_cust}`.toLowerCase() === "true",
            language: language || data.language,
            playerCode: `${data.cust_id}`,
            gameGroup: data.game_group,
            brandId: merchant.brandId,
            merchantCode: merchant.code,
            merchantType: merchant.type,
            ipmSessionId: data.cust_session_id,
            oldGameCode: info?.gameCode
        };

        if (data.disable_offers !== undefined) {
            tokenData.disablePlayerPhantomFeatures = data.disable_offers;
        }

        if (playMode) {
            tokenData.playmode = playMode;
        }

        if (data.rci && !Number.isNaN(+data.rci)) {
            tokenData.rci = +data.rci;
            tokenData.rce = +data.rce || 0;
        }

        if (data.nickname) {
            tokenData.nickname = data.nickname;
        }

        if (data.max_total_bet) {
            tokenData.dynamicMaxTotalBetLimit = data.max_total_bet;
        }

        return tokenData;

    }

    private async getStartGameTokenDataFromPreviousToken(
        merchant: MerchantInfo,
        gameInfo: GameInfo,
        initRequest: SeamlessGameInitRequest): Promise<SeamlessStartGameTokenData> {

        const tokenData = initRequest.previousStartTokenData as SeamlessStartGameTokenData;
        const sessionId = await this.getCustomerSessionId(merchant, gameInfo, {
            ...initRequest,
            gameCode: tokenData.gameCode,
            playerCode: tokenData.playerCode,
            ipmSessionId: tokenData.ipmSessionId
        });

        const startGameTokenData: SeamlessStartGameTokenData = {
            ...gameInfo,
            ipmSessionId: sessionId,
            brandId: tokenData.brandId,
            merchantType: tokenData.merchantType,
            merchantCode: tokenData.merchantCode,
            playerCode: tokenData.playerCode,
            gameGroup: tokenData.gameGroup,
            currency: tokenData.currency,
            test: tokenData.test,
            country: tokenData.country,
            language: tokenData.language,
            playmode: initRequest.playmode || tokenData.playmode,
            siteUrl: tokenData.siteUrl,
            oldGameCode: gameInfo.gameCode,
            ...(tokenData.disablePlayerPhantomFeatures !== undefined &&
                { disablePlayerPhantomFeatures: tokenData.disablePlayerPhantomFeatures })
        };

        if (tokenData.rci) {
            startGameTokenData.rci = tokenData.rci;
        }
        if (tokenData.rce) {
            startGameTokenData.rce = tokenData.rce;
        }
        if (tokenData.dynamicMaxTotalBetLimit) {
            startGameTokenData.dynamicMaxTotalBetLimit = tokenData.dynamicMaxTotalBetLimit;
        }
        return startGameTokenData;
    }

    private isChooseGame(initRequest: SeamlessGameInitRequest): boolean {
        return !!initRequest.previousStartTokenData;
    }

    private isLobbyRequest(initRequest: SeamlessGameInitRequest): boolean {
        return !!(initRequest.customerSessionId || initRequest.ipmSessionId);
    }

    @measure({ name: "GameService.getGameTokenInfo", isAsync: true })
    public async getGameTokenInfo(merchant: MerchantInfo,
                                  tokenData: SeamlessStartGameTokenData,
                                  currency: string,
                                  transferEnabled: boolean): Promise<MerchantGameTokenInfo<SeamlessAuthTokenData>> {

        const gameTokenData: SeamlessAuthTokenData = {
            ipmSessionId: tokenData.ipmSessionId,
            playerCode: tokenData.playerCode,
            gameCode: tokenData.gameCode,
            brandId: tokenData.brandId,
            currency,
            merchantType: merchant.type,
            merchantCode: merchant.code,
            test: tokenData.test,
            transferEnabled,
            isPromoInternal: merchant.params.isPromoInternal || false,
            playmode: tokenData.playmode,
            oldGameCode: tokenData.oldGameCode,
            hasLobby: tokenData.hasLobby,
            ...(tokenData.disablePlayerPhantomFeatures !== undefined &&
                { disablePlayerPhantomFeatures: tokenData.disablePlayerPhantomFeatures })
        };

        if (tokenData.nickname) {
            gameTokenData.nickname = tokenData.nickname;
        }

        return {
            gameTokenData,
            operatorSiteExternalCode: tokenData.siteUrl
        };
    }

    public async loginTerminalPlayer(
        merchant: MerchantInfo,
        initRequest: LoginTerminalRequest): Promise<LoginTerminalResponse<SeamlessStartGameTokenData>> {
        const ticketUrl = merchant.params.sameUrlForTerminalLoginAndTicket ?
            "api/validate_ticket" :
            "api/validate_terminal_ticket";

        const data = await this.verifyToken(merchant, initRequest.ticket, undefined, ticketUrl);
        const currency = CurrencyConverter.fromIPMCurrency(data.currency_code, merchant);

        return {
            tokenData: this.mapSeamlessToSWToken(merchant, data, {
                gameCode: undefined,
                providerCode: undefined,
                providerGameCode: undefined,
                currency,
                hasLobby: false,
                siteUrl: undefined,
                playMode: playModeConverter(initRequest.playmode, currency, merchant)
            }),
            sessionId: data.cust_session_id
        };
    }

    public async keepAlive(merchant: MerchantInfo, gameToken: SeamlessAuthTokenData): Promise<void> {
        throw new MerchantInternalError("keepAlive is not supported.");
    }
}

export function getGameService(apiService: SeamlessAPIService, regulation?): GameService {
    const service = new GameService(apiService);
    return applyRegulation(service, regulation);
}
